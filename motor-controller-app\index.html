<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Motor Controller</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <!-- 标题栏 -->
        <header class="header">
            <h1>ESP32S3 Motor Controller</h1>
            <div class="connection-status" id="connectionStatus">
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">Disconnected</span>
            </div>
        </header>

        <!-- 连接配置区域 -->
        <section class="connection-panel">
            <div class="connection-config">
                <div class="input-group">
                    <label for="ipAddress">IP Address:</label>
                    <input type="text" id="ipAddress" value="*************" placeholder="*************">
                </div>
                <div class="input-group">
                    <label for="port">Port:</label>
                    <input type="number" id="port" value="3000" placeholder="3000">
                </div>
                <button id="testConnection" class="btn btn-secondary">Test Connection</button>
                <button id="debugTest" class="btn btn-info">Debug Test</button>
            </div>
        </section>

        <!-- 控制面板 -->
        <section class="control-panel">
            <div class="control-buttons">
                <button id="startMotors" class="btn btn-success" disabled>START</button>
                <button id="stopMotors" class="btn btn-danger" disabled>STOP</button>
                <button id="sendPWM" class="btn btn-primary" disabled>Send PWM Values</button>
                <button id="clearAll" class="btn btn-warning">Clear All</button>
            </div>
            
            <div class="preset-controls">
                <label for="presetValue">Preset Value:</label>
                <input type="range" id="presetValue" min="0" max="255" value="128">
                <span id="presetValueDisplay">128</span>
                <button id="applyPreset" class="btn btn-info">Apply to All</button>
            </div>
        </section>

        <!-- PWM控制矩阵 -->
        <section class="pwm-matrix">
            <h3>PWM Control Matrix (10x10) - Direct Input</h3>
            <div class="matrix-container">
                <div class="matrix-grid" id="pwmMatrix">
                    <!-- 10x10 PWM控制器将通过JavaScript生成 -->
                </div>
            </div>
        </section>

        <!-- 状态信息 -->
        <section class="status-panel">
            <div class="status-info">
                <h4>Device Status</h4>
                <div id="deviceStatus" class="device-status">
                    <p>No device information available</p>
                </div>
            </div>
            
            <div class="log-panel">
                <h4>Communication Log</h4>
                <div class="log-controls">
                    <label>
                        <input type="checkbox" id="showSentMessages" checked> Show Sent
                    </label>
                    <label>
                        <input type="checkbox" id="showReceivedMessages" checked> Show Received
                    </label>
                    <label>
                        <input type="checkbox" id="showErrors" checked> Show Errors
                    </label>
                    <button id="clearLog" class="btn btn-small">Clear Log</button>
                </div>
                <div id="activityLog" class="activity-log">
                    <p class="log-entry info">Application started</p>
                </div>
            </div>
        </section>
    </div>

    <script src="renderer.js"></script>
</body>
</html>

const { ipc<PERSON>enderer } = require('electron');

class MotorController {
    constructor() {
        this.pwmValues = new Array(100).fill(0); // 100个电机的PWM值
        this.isConnected = false;
        this.currentIP = '';
        this.currentPort = 3000;
        
        this.initializeUI();
        this.bindEvents();
        this.createPWMMatrix();
    }

    initializeUI() {
        // 初始化UI元素引用
        this.elements = {
            ipAddress: document.getElementById('ipAddress'),
            port: document.getElementById('port'),
            testConnection: document.getElementById('testConnection'),
            debugTest: document.getElementById('debugTest'),
            startMotors: document.getElementById('startMotors'),
            stopMotors: document.getElementById('stopMotors'),
            sendPWM: document.getElementById('sendPWM'),
            clearAll: document.getElementById('clearAll'),
            sendZeros: document.getElementById('sendZeros'),
            presetValue: document.getElementById('presetValue'),
            presetValueDisplay: document.getElementById('presetValueDisplay'),
            applyPreset: document.getElementById('applyPreset'),
            statusIndicator: document.getElementById('statusIndicator'),
            statusText: document.getElementById('statusText'),
            deviceStatus: document.getElementById('deviceStatus'),
            activityLog: document.getElementById('activityLog'),
            clearLog: document.getElementById('clearLog'),
            pwmMatrix: document.getElementById('pwmMatrix'),
            showSentMessages: document.getElementById('showSentMessages'),
            showReceivedMessages: document.getElementById('showReceivedMessages'),
            showErrors: document.getElementById('showErrors')
        };
    }

    bindEvents() {
        // 连接测试
        this.elements.testConnection.addEventListener('click', () => this.testConnection());
        this.elements.debugTest.addEventListener('click', () => this.debugTest());
        
        // 电机控制
        this.elements.startMotors.addEventListener('click', () => this.startMotors());
        this.elements.stopMotors.addEventListener('click', () => this.stopMotors());
        this.elements.sendPWM.addEventListener('click', () => this.sendPWMValues());
        this.elements.sendZeros.addEventListener('click', () => this.sendAllZeros());

        // 预设值控制
        this.elements.presetValue.addEventListener('input', (e) => {
            this.elements.presetValueDisplay.textContent = e.target.value;
        });
        this.elements.applyPreset.addEventListener('click', () => this.applyPresetToAll());
        this.elements.clearAll.addEventListener('click', () => this.clearAllPWM());
        
        // 日志控制
        this.elements.clearLog.addEventListener('click', () => this.clearLog());
        this.elements.showSentMessages.addEventListener('change', () => this.filterLogs());
        this.elements.showReceivedMessages.addEventListener('change', () => this.filterLogs());
        this.elements.showErrors.addEventListener('change', () => this.filterLogs());

        // IP和端口变化监听
        this.elements.ipAddress.addEventListener('change', () => this.updateConnection());
        this.elements.port.addEventListener('change', () => this.updateConnection());
    }

    createPWMMatrix() {
        const matrix = this.elements.pwmMatrix;
        matrix.innerHTML = '';

        for (let i = 0; i < 100; i++) {
            const controlDiv = document.createElement('div');
            controlDiv.className = 'pwm-control';
            controlDiv.innerHTML = `
                <div class="motor-label">M${i}</div>
                <input type="number" class="pwm-input" min="0" max="255" value="0"
                       data-motor="${i}" placeholder="0">
            `;

            const input = controlDiv.querySelector('.pwm-input');

            input.addEventListener('input', (e) => {
                const motorIndex = parseInt(e.target.dataset.motor);
                let value = parseInt(e.target.value) || 0;

                // 验证输入范围
                if (value < 0) {
                    value = 0;
                    e.target.value = 0;
                } else if (value > 255) {
                    value = 255;
                    e.target.value = 255;
                }

                this.pwmValues[motorIndex] = value;

                // 视觉反馈
                controlDiv.classList.remove('error');
                if (value > 0) {
                    controlDiv.classList.add('active');
                } else {
                    controlDiv.classList.remove('active');
                }
            });

            input.addEventListener('blur', (e) => {
                const value = parseInt(e.target.value) || 0;
                if (value < 0 || value > 255) {
                    controlDiv.classList.add('error');
                    input.classList.add('error');
                } else {
                    controlDiv.classList.remove('error');
                    input.classList.remove('error');
                }
            });

            input.addEventListener('focus', (e) => {
                controlDiv.classList.remove('error');
                input.classList.remove('error');
            });

            matrix.appendChild(controlDiv);
        }
    }

    updateConnection() {
        this.currentIP = this.elements.ipAddress.value;
        this.currentPort = parseInt(this.elements.port.value);
        this.setConnectionStatus(false);
    }

    async testConnection() {
        this.addLog('Testing connection...', 'info');
        this.elements.testConnection.disabled = true;

        try {
            this.addLog(`→ Sending: STATUS to ${this.currentIP}:${this.currentPort}`, 'sent');
            const result = await ipcRenderer.invoke('test-connection', this.currentIP, this.currentPort);

            if (result.success) {
                this.setConnectionStatus(true);
                this.updateDeviceStatus(result.data);
                this.addLog(`← Received: ${JSON.stringify(result.data)}`, 'received');
                this.addLog(`Connected to ${this.currentIP}:${this.currentPort}`, 'info');
            } else {
                this.setConnectionStatus(false);
                this.addLog(`Connection failed: ${result.error}`, 'error');
            }
        } catch (error) {
            this.setConnectionStatus(false);
            this.addLog(`Connection error: ${error.message}`, 'error');
        }

        this.elements.testConnection.disabled = false;
    }

    setConnectionStatus(connected) {
        this.isConnected = connected;
        
        if (connected) {
            this.elements.statusIndicator.classList.add('connected');
            this.elements.statusText.textContent = `Connected to ${this.currentIP}:${this.currentPort}`;
        } else {
            this.elements.statusIndicator.classList.remove('connected');
            this.elements.statusText.textContent = 'Disconnected';
        }
        
        // 启用/禁用控制按钮
        this.elements.startMotors.disabled = !connected;
        this.elements.stopMotors.disabled = !connected;
        this.elements.sendPWM.disabled = !connected;
        this.elements.sendZeros.disabled = !connected;
    }

    updateDeviceStatus(statusData) {
        const statusDiv = this.elements.deviceStatus;
        statusDiv.innerHTML = `
            <p><strong>System Running:</strong> ${statusData.system_running ? 'Yes' : 'No'}</p>
            <p><strong>WiFi Connected:</strong> ${statusData.wifi_connected ? 'Yes' : 'No'}</p>
            <p><strong>WiFi IP:</strong> ${statusData.wifi_ip || 'N/A'}</p>
            <p><strong>Available Chips:</strong> ${statusData.available_chips || 'N/A'}</p>
            <p><strong>Max Motors:</strong> ${statusData.max_motors || 'N/A'}</p>
            <p><strong>Timestamp:</strong> ${new Date(statusData.timestamp).toLocaleTimeString()}</p>
        `;
    }

    async startMotors() {
        if (!this.isConnected) return;

        this.addLog('Starting motors...', 'info');

        try {
            this.addLog(`→ Sending: START`, 'sent');
            const result = await ipcRenderer.invoke('send-udp-command', this.currentIP, this.currentPort, 'START');

            if (result.success) {
                this.addLog(`← Received: ${JSON.stringify(result.data)}`, 'received');
                if (result.data.status === 'success') {
                    this.addLog('Motors started successfully', 'info');
                } else {
                    this.addLog(`Start failed: ${result.data.message || 'Unknown error'}`, 'error');
                }
            } else {
                // 这里是ESP32返回的错误或网络错误
                this.addLog(`Start failed: ${result.error}`, 'error');
            }
        } catch (error) {
            this.addLog(`Start error: ${error.message}`, 'error');
        }
    }

    async stopMotors() {
        if (!this.isConnected) return;

        this.addLog('Stopping motors...', 'info');

        try {
            this.addLog(`→ Sending: STOP`, 'sent');
            const result = await ipcRenderer.invoke('send-udp-command', this.currentIP, this.currentPort, 'STOP');

            if (result.success) {
                this.addLog(`← Received: ${JSON.stringify(result.data)}`, 'received');
                if (result.data.status === 'success') {
                    this.addLog('Motors stopped successfully', 'info');
                } else {
                    this.addLog(`Stop failed: ${result.data.message || 'Unknown error'}`, 'error');
                }
            } else {
                this.addLog(`Stop failed: ${result.error}`, 'error');
            }
        } catch (error) {
            this.addLog(`Stop error: ${error.message}`, 'error');
        }
    }

    async sendPWMValues() {
        if (!this.isConnected) return;

        // 发送所有已修改的PWM值（包括0值）
        const changedPWM = [];
        for (let i = 0; i < this.pwmValues.length; i++) {
            // 检查输入框是否有值（包括0）
            const input = document.querySelector(`[data-motor="${i}"]`);
            if (input && input.value !== '') {
                const value = parseInt(input.value) || 0;
                changedPWM.push({ motor: i, pwm: value });
            }
        }

        if (changedPWM.length === 0) {
            this.addLog('No PWM values to send (no motors configured)', 'info');
            return;
        }

        this.addLog(`Sending ${changedPWM.length} PWM values...`, 'info');
        this.elements.sendPWM.disabled = true;

        try {
            let successCount = 0;
            let errorCount = 0;

            // 逐个发送SET命令
            for (const item of changedPWM) {
                const command = `SET,${item.motor},${item.pwm}`;
                this.addLog(`→ Sending: ${command}`, 'sent');

                const result = await ipcRenderer.invoke('send-udp-command', this.currentIP, this.currentPort, command);

                if (result.success) {
                    this.addLog(`← Received: ${JSON.stringify(result.data)}`, 'received');
                    if (result.data.status === 'success') {
                        successCount++;
                    } else {
                        this.addLog(`Failed to set motor ${item.motor}: ${result.data.message || 'Unknown error'}`, 'error');
                        errorCount++;
                    }
                } else {
                    this.addLog(`Failed to set motor ${item.motor}: ${result.error}`, 'error');
                    errorCount++;
                }

                // 小延迟避免UDP包丢失
                await new Promise(resolve => setTimeout(resolve, 30));
            }

            this.addLog(`PWM send completed: ${successCount} success, ${errorCount} errors`, 'info');
        } catch (error) {
            this.addLog(`Send PWM error: ${error.message}`, 'error');
        }

        this.elements.sendPWM.disabled = false;
    }

    applyPresetToAll() {
        const presetValue = parseInt(this.elements.presetValue.value);

        // 更新所有输入框和值
        const inputs = document.querySelectorAll('.pwm-input');
        const controls = document.querySelectorAll('.pwm-control');

        inputs.forEach((input, index) => {
            input.value = presetValue;
            this.pwmValues[index] = presetValue;

            if (presetValue > 0) {
                controls[index].classList.add('active');
            } else {
                controls[index].classList.remove('active');
            }

            // 清除错误状态
            controls[index].classList.remove('error');
            input.classList.remove('error');
        });

        this.addLog(`Applied preset value ${presetValue} to all motors`, 'info');
    }

    clearAllPWM() {
        // 清除所有PWM值，设置为0
        const inputs = document.querySelectorAll('.pwm-input');
        const controls = document.querySelectorAll('.pwm-control');

        inputs.forEach((input, index) => {
            input.value = '0';  // 明确设置为字符串'0'
            this.pwmValues[index] = 0;
            controls[index].classList.remove('active');
            controls[index].classList.remove('error');
            input.classList.remove('error');
        });

        this.addLog('All PWM values set to 0 (ready to send)', 'info');
    }

    addLog(message, type = 'info') {
        const logDiv = this.elements.activityLog;
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('p');
        logEntry.className = `log-entry ${type}`;
        logEntry.textContent = `[${timestamp}] ${message}`;
        logEntry.dataset.type = type;

        logDiv.appendChild(logEntry);
        logDiv.scrollTop = logDiv.scrollHeight;

        // 应用当前过滤器
        this.filterLogs();

        // 限制日志条目数量
        const entries = logDiv.querySelectorAll('.log-entry');
        if (entries.length > 200) {
            entries[0].remove();
        }
    }

    filterLogs() {
        const showSent = this.elements.showSentMessages.checked;
        const showReceived = this.elements.showReceivedMessages.checked;
        const showErrors = this.elements.showErrors.checked;

        const entries = document.querySelectorAll('.log-entry');
        entries.forEach(entry => {
            const type = entry.dataset.type;
            let shouldShow = true;

            if (type === 'sent' && !showSent) {
                shouldShow = false;
            } else if (type === 'received' && !showReceived) {
                shouldShow = false;
            } else if (type === 'error' && !showErrors) {
                shouldShow = false;
            }

            if (shouldShow) {
                entry.classList.remove('hidden');
            } else {
                entry.classList.add('hidden');
            }
        });
    }

    clearLog() {
        this.elements.activityLog.innerHTML = '<p class="log-entry info" data-type="info">[' + new Date().toLocaleTimeString() + '] Log cleared</p>';
    }

    async debugTest() {
        if (!this.isConnected) {
            this.addLog('Please connect first', 'error');
            return;
        }

        this.addLog('Starting debug test - testing START command...', 'info');

        try {
            // 直接测试START命令，不管成功失败都显示完整响应
            this.addLog(`→ Sending: START`, 'sent');
            const result = await ipcRenderer.invoke('send-udp-command', this.currentIP, this.currentPort, 'START');

            this.addLog(`Raw result object: ${JSON.stringify(result)}`, 'info');

            if (result.success) {
                this.addLog(`← ESP32 Response: ${JSON.stringify(result.data)}`, 'received');
                this.addLog(`Response status: ${result.data.status}`, 'info');
                this.addLog(`Response message: ${result.data.message || 'No message'}`, 'info');

                if (result.data.status === 'success') {
                    this.addLog(`✓ START command succeeded!`, 'info');
                } else {
                    this.addLog(`✗ START command failed with ESP32 error: ${result.data.message}`, 'error');
                }
            } else {
                this.addLog(`✗ Network/parsing error: ${result.error}`, 'error');
            }

            // 测试SET命令
            await new Promise(resolve => setTimeout(resolve, 500));

            this.addLog(`→ Sending: SET,0,100`, 'sent');
            const setResult = await ipcRenderer.invoke('send-udp-command', this.currentIP, this.currentPort, 'SET,0,100');

            this.addLog(`SET Raw result: ${JSON.stringify(setResult)}`, 'info');

            if (setResult.success) {
                this.addLog(`← ESP32 Response: ${JSON.stringify(setResult.data)}`, 'received');
                if (setResult.data.status === 'success') {
                    this.addLog(`✓ SET command succeeded!`, 'info');
                } else {
                    this.addLog(`✗ SET command failed: ${setResult.data.message}`, 'error');
                }
            } else {
                this.addLog(`✗ SET Network error: ${setResult.error}`, 'error');
            }

        } catch (error) {
            this.addLog(`Debug test error: ${error.message}`, 'error');
        }

        this.addLog('Debug test completed', 'info');
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new MotorController();
});

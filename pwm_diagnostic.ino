/***************************************************
  ESP32S3 PWM芯片诊断程序
  用于检测和诊断PWM芯片连接问题
  
  功能：
  - I2C设备扫描
  - PWM芯片连接测试
  - I2C通信质量测试
  - 硬件连接诊断
  
  串口波特率: 115200
 ****************************************************/

#include <Wire.h>
#include <Adafruit_PWMServoDriver.h>

// PWM芯片配置
#define NUM_PWM_CHIPS 7
#define CHANNELS_PER_CHIP 16

// 实际检测到的PWM芯片地址列表
uint8_t pwmChipAddresses[NUM_PWM_CHIPS] = {0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46};

// 使用ESP32S3默认I2C引脚（与motor_controller.ino保持一致）

String inputBuffer = "";

void setup() {
  Serial.begin(115200);
  delay(2000); // 等待串口稳定
  
  Serial.println("=================================");
  Serial.println("ESP32S3 PWM芯片诊断程序");
  Serial.println("=================================");
  Serial.println();
  
  // 显示系统信息
  showSystemInfo();
  
  // 初始化I2C
  initializeI2C();
  
  // 自动运行基础诊断
  runBasicDiagnostics();
  
  // 显示可用命令
  showCommands();
}

void loop() {
  // 处理串口输入
  if (Serial.available()) {
    char c = Serial.read();
    if (c == '\n' || c == '\r') {
      if (inputBuffer.length() > 0) {
        processCommand(inputBuffer);
        inputBuffer = "";
      }
    } else {
      inputBuffer += c;
    }
  }
}

void showSystemInfo() {
  Serial.println("系统信息:");
  Serial.print("  芯片型号: ");
  Serial.println("ESP32S3");
  Serial.println("  I2C引脚: 使用ESP32S3默认引脚");
  Serial.print("  PWM芯片地址列表: ");
  for (int i = 0; i < NUM_PWM_CHIPS; i++) {
    Serial.print("0x");
    Serial.print(pwmChipAddresses[i], HEX);
    if (i < NUM_PWM_CHIPS - 1) Serial.print(", ");
  }
  Serial.println();
  Serial.println();
}

void initializeI2C() {
  Serial.println("初始化I2C总线...");

  // 尝试不同的I2C速度
  uint32_t speeds[] = {100000, 50000, 10000}; // 100kHz, 50kHz, 10kHz (更慢更稳定)
  String speedNames[] = {"100kHz", "50kHz", "10kHz"};

  for (int i = 0; i < 3; i++) {
    Serial.print("尝试I2C速度: ");
    Serial.println(speedNames[i]);

    Serial.println("  初始化I2C引脚...");
    Wire.begin(); // 使用ESP32S3默认引脚，与motor_controller.ino保持一致

    Serial.println("  设置I2C时钟...");
    Wire.setClock(speeds[i]);

    Serial.println("  等待稳定...");
    delay(200);

    // 测试I2C通信
    if (testI2CCommunication()) {
      Serial.print("✓ I2C初始化成功，速度: ");
      Serial.println(speedNames[i]);
      return;
    }

    Serial.println("  关闭I2C...");
    Wire.end();
    delay(500);
  }

  Serial.println("⚠ 警告: 所有I2C速度测试失败");
  Serial.println("  可能的问题:");
  Serial.println("  - I2C线路未连接");
  Serial.println("  - 没有上拉电阻");
  Serial.println("  - 硬件故障");
}

bool testI2CCommunication() {
  Serial.print("    测试I2C通信...");

  // 尝试与一个常见地址通信，添加超时保护
  unsigned long startTime = millis();
  Wire.beginTransmission(0x40);
  uint8_t error = Wire.endTransmission();
  unsigned long duration = millis() - startTime;

  Serial.print(" 耗时: ");
  Serial.print(duration);
  Serial.print("ms, 错误代码: ");
  Serial.println(error);

  return (error == 0 || error == 2); // 0=成功, 2=地址NACK但总线正常
}

void runBasicDiagnostics() {
  Serial.println("运行基础诊断...");
  Serial.println();
  
  // 1. I2C设备扫描
  scanI2CDevices();
  Serial.println();
  
  // 2. PWM芯片特定测试
  testPWMChips();
  Serial.println();
  
  // 3. 连接质量测试
  testConnectionQuality();
  Serial.println();
}

void scanI2CDevices() {
  Serial.println("=== I2C设备扫描 ===");
  Serial.println("扫描地址范围: 0x08 - 0x77");
  
  int deviceCount = 0;
  bool foundPWMChips[NUM_PWM_CHIPS] = {false};
  
  for (uint8_t address = 8; address < 120; address++) {
    Wire.beginTransmission(address);
    uint8_t error = Wire.endTransmission();
    
    if (error == 0) {
      Serial.print("✓ 发现设备: 0x");
      if (address < 16) Serial.print("0");
      Serial.print(address, HEX);
      
      // 检查是否是已知的PWM芯片地址
      for (int j = 0; j < NUM_PWM_CHIPS; j++) {
        if (address == pwmChipAddresses[j]) {
          foundPWMChips[j] = true;
          Serial.print(" (PWM芯片 #");
          Serial.print(j + 1);
          Serial.print(")");
          break;
        }
      }
      Serial.println();
      deviceCount++;
    }
    delay(10);
  }
  
  Serial.print("总计发现设备: ");
  Serial.println(deviceCount);
  
  // 报告PWM芯片状态
  Serial.println("PWM芯片检测结果:");
  for (int i = 0; i < NUM_PWM_CHIPS; i++) {
    Serial.print("  芯片 #");
    Serial.print(i + 1);
    Serial.print(" (0x");
    Serial.print(pwmChipAddresses[i], HEX);
    Serial.print("): ");
    if (foundPWMChips[i]) {
      Serial.println("✓ 已检测到");
    } else {
      Serial.println("✗ 未检测到");
    }
  }
}

void testPWMChips() {
  Serial.println("=== PWM芯片功能测试 ===");
  
  for (int i = 0; i < NUM_PWM_CHIPS; i++) {
    uint8_t address = pwmChipAddresses[i];
    Serial.print("测试PWM芯片 #");
    Serial.print(i + 1);
    Serial.print(" (0x");
    Serial.print(address, HEX);
    Serial.print("): ");
    
    // 检查设备是否响应
    Wire.beginTransmission(address);
    uint8_t error = Wire.endTransmission();
    
    if (error == 0) {
      // 尝试初始化PWM芯片
      Adafruit_PWMServoDriver pwm = Adafruit_PWMServoDriver(address);
      pwm.begin();
      
      // 尝试设置PWM频率
      pwm.setPWMFreq(1000);
      
      // 尝试设置一个PWM通道
      pwm.setPWM(0, 0, 4096); // 关闭通道0
      
      Serial.println("✓ 功能正常");
    } else {
      Serial.print("✗ 通信失败 (错误代码: ");
      Serial.print(error);
      Serial.println(")");
    }
    delay(100);
  }
}

void testConnectionQuality() {
  Serial.println("=== 连接质量测试 ===");
  
  for (int i = 0; i < NUM_PWM_CHIPS; i++) {
    uint8_t address = pwmChipAddresses[i];
    Serial.print("测试地址 0x");
    Serial.print(address, HEX);
    Serial.print(": ");
    
    int successCount = 0;
    int totalTests = 10;
    
    for (int test = 0; test < totalTests; test++) {
      Wire.beginTransmission(address);
      if (Wire.endTransmission() == 0) {
        successCount++;
      }
      delay(10);
    }
    
    float successRate = (float)successCount / totalTests * 100;
    Serial.print(successRate, 1);
    Serial.print("% 成功率 (");
    Serial.print(successCount);
    Serial.print("/");
    Serial.print(totalTests);
    Serial.print(")");
    
    if (successRate >= 90) {
      Serial.println(" ✓ 优秀");
    } else if (successRate >= 70) {
      Serial.println(" ⚠ 一般");
    } else {
      Serial.println(" ✗ 差");
    }
  }
}

void showCommands() {
  Serial.println("=================================");
  Serial.println("可用诊断命令:");
  Serial.println("  SCAN     - 重新扫描I2C设备");
  Serial.println("  TEST     - 重新测试PWM芯片");
  Serial.println("  QUALITY  - 重新测试连接质量");
  Serial.println("  MAP      - 显示电机与芯片映射关系");
  Serial.println("  MOTOR,n  - 测试指定电机 (例如: MOTOR,0)");
  Serial.println("  INFO     - 显示系统信息");
  Serial.println("  HELP     - 显示帮助信息");
  Serial.println("=================================");
}

// 显示电机与PWM芯片的映射关系
void showMotorChipMapping() {
  Serial.println("=== 电机与PWM芯片映射关系 ===");

  for (int i = 0; i < NUM_PWM_CHIPS; i++) {
    Serial.print("PWM芯片 #");
    Serial.print(i + 1);
    Serial.print(" (0x");
    Serial.print(pwmChipAddresses[i], HEX);
    Serial.print(") - 电机 ");

    int startMotor = i * CHANNELS_PER_CHIP;
    int endMotor = (i + 1) * CHANNELS_PER_CHIP - 1;
    if (endMotor >= 100) endMotor = 99; // 最多支持0-99个电机

    Serial.print(startMotor);
    Serial.print("-");
    Serial.print(endMotor);
    Serial.println();
  }
  Serial.println("=============================");
}

// 测试指定电机
void testMotor(int motorNum) {
  if (motorNum < 0 || motorNum >= 100) {
    Serial.println("错误: 电机号超出范围 (0-99)");
    return;
  }

  // 计算对应的芯片和通道
  int motorIndex = motorNum;
  int chipIndex = motorIndex / CHANNELS_PER_CHIP;
  int channel = motorIndex % CHANNELS_PER_CHIP;

  if (chipIndex >= NUM_PWM_CHIPS) {
    Serial.println("错误: 电机号超出硬件支持范围");
    return;
  }

  uint8_t address = pwmChipAddresses[chipIndex];

  Serial.print("测试电机 ");
  Serial.print(motorNum);
  Serial.print(" (芯片 #");
  Serial.print(chipIndex + 1);
  Serial.print(" 0x");
  Serial.print(address, HEX);
  Serial.print(", 通道 ");
  Serial.print(channel);
  Serial.println(")");

  // 检查芯片是否响应
  Wire.beginTransmission(address);
  if (Wire.endTransmission() != 0) {
    Serial.println("✗ 芯片无响应");
    return;
  }

  // 初始化PWM芯片
  Adafruit_PWMServoDriver pwm = Adafruit_PWMServoDriver(address);
  pwm.begin();
  pwm.setOscillatorFrequency(25000000);
  pwm.setPWMFreq(1000);

  Serial.println("开始PWM测试序列...");

  // 测试序列：关闭 -> 25% -> 50% -> 75% -> 100% -> 关闭
  uint16_t testValues[] = {4096, 1024, 2048, 3072, 4095, 4096};
  String testNames[] = {"关闭", "25%", "50%", "75%", "100%", "关闭"};

  for (int i = 0; i < 6; i++) {
    Serial.print("  设置为 ");
    Serial.print(testNames[i]);
    Serial.print("...");

    if (testValues[i] == 4096) {
      pwm.setPWM(channel, 0, 4096); // 关闭
    } else {
      pwm.setPWM(channel, 0, testValues[i]);
    }

    Serial.println(" 完成");
    delay(1000); // 等待1秒观察效果
  }

  Serial.println("✓ 电机测试完成");
}

void processCommand(String command) {
  command.trim();
  command.toUpperCase();
  
  if (command == "SCAN") {
    scanI2CDevices();
  }
  else if (command == "TEST") {
    testPWMChips();
  }
  else if (command == "QUALITY") {
    testConnectionQuality();
  }
  else if (command == "MAP") {
    showMotorChipMapping();
  }
  else if (command == "INFO") {
    showSystemInfo();
  }
  else if (command.startsWith("MOTOR,")) {
    // 解析MOTOR命令: MOTOR,电机号
    int commaIndex = command.indexOf(',');
    if (commaIndex > 0) {
      String motorStr = command.substring(commaIndex + 1);
      int motorNum = motorStr.toInt();
      if (motorNum > 0) {
        testMotor(motorNum);
      } else {
        Serial.println("错误: 无效的电机号");
      }
    } else {
      Serial.println("错误: MOTOR命令格式错误，应为 MOTOR,电机号");
    }
  }
  else if (command == "HELP") {
    showCommands();
    Serial.println();
    Serial.println("故障排除建议:");
    Serial.println("1. 检查I2C线路连接 (SDA/SCL)");
    Serial.println("2. 确认PWM芯片供电正常");
    Serial.println("3. 检查I2C上拉电阻 (通常4.7kΩ)");
    Serial.println("4. 确认PWM芯片地址设置正确");
    Serial.println("5. 检查线路长度和信号质量");
    Serial.println();
    Serial.println("使用示例:");
    Serial.println("  MOTOR,0   - 测试0号电机");
    Serial.println("  MOTOR,16  - 测试16号电机");
    Serial.println("  MAP       - 查看电机映射关系");
  }
  else {
    Serial.println("未知命令，输入 HELP 查看可用命令");
  }
  Serial.println();
}

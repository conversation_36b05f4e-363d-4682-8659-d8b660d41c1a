/***************************************************
  ESP32S3 震动电机驱动器控制程序
  支持7颗PWM芯片（地址0x40~0x46），控制最多112个电机
  通过串口协议控制1~100个电机的PWM值
  
  串口协议：
  - 设置电机PWM: "SET,电机号,PWM值\n" (例如: SET,0,255) - 仅缓存，不立即执行
  - 启动执行: "START\n" - 统一执行所有缓存的PWM值
  - 停止输出: "STOP\n" - 所有电机输出设为0
  - 查询状态: "STATUS\n"

  电机号范围: 0~99
  PWM值范围: 0~255 (0=关闭, 255=最大强度)
  串口波特率: 115200
 ****************************************************/

#include <Wire.h>
#include <Adafruit_PWMServoDriver.h>
#include <WiFi.h>
#include <Preferences.h>

// PWM芯片数量和地址配置
#define NUM_PWM_CHIPS 7
#define MAX_MOTORS 100
#define CHANNELS_PER_CHIP 16

// 实际检测到的PWM芯片地址列表
uint8_t pwmChipAddresses[NUM_PWM_CHIPS] = {0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46};

// 创建PWM驱动器数组
Adafruit_PWMServoDriver pwmChips[NUM_PWM_CHIPS];

// 电机PWM值存储数组 (0~255)
uint8_t motorPWM[MAX_MOTORS];

// 系统状态
bool systemRunning = false;
String inputBuffer = "";

// PWM芯片可用状态
bool chipAvailable[NUM_PWM_CHIPS];

// WiFi配置和状态 - 基于官方例程
Preferences preferences;
String wifiSSID = "";
String wifiPassword = "";
bool wifiAutoConnect = false;
bool wifiScanInProgress = false;

void setup() {
  Serial.begin(115200);
  Serial.println("ESP32S3 震动电机驱动器启动中...");
  
  // 初始化I2C
  Wire.begin();
  Wire.setClock(100000); // 降低到100kHz I2C速度，提高稳定性

  Serial.println("开始检测PWM芯片...");

  // 按实际地址列表初始化所有PWM芯片
  for (int i = 0; i < NUM_PWM_CHIPS; i++) {
    uint8_t address = pwmChipAddresses[i];
    chipAvailable[i] = false;

    Serial.print("初始化PWM芯片 #");
    Serial.print(i + 1);
    Serial.print(" (地址: 0x");
    Serial.print(address, HEX);
    Serial.print(")... ");

    // 检测芯片是否存在
    Wire.beginTransmission(address);
    if (Wire.endTransmission() == 0) {
      // 芯片存在，尝试初始化
      pwmChips[i] = Adafruit_PWMServoDriver(address);
      pwmChips[i].begin();
      pwmChips[i].setOscillatorFrequency(25000000);
      pwmChips[i].setPWMFreq(1000); // 1kHz PWM频率
      chipAvailable[i] = true;

      Serial.println("✓ 成功");
    } else {
      Serial.println("✗ 失败");
    }
    delay(100); // 给每个芯片一些初始化时间
  }
  
  // 初始化电机PWM值数组
  for (int i = 0; i < MAX_MOTORS; i++) {
    motorPWM[i] = 0;
  }
  
  // 确保所有电机都是关闭状态
  stopAllMotors();

  // 初始化WiFi配置 - 参考官方例程
  initializeWiFi();

  Serial.println("系统初始化完成!");
  Serial.println("支持的指令:");
  Serial.println("  SET,电机号,PWM值  - 设置电机PWM缓存值 (电机号:0-99, PWM:0-255)");
  Serial.println("  START            - 执行所有缓存的PWM值");
  Serial.println("  STOP             - 停止所有电机输出(设为0)");
  Serial.println("  STATUS           - 查询系统状态");
  Serial.println("  WIFI_SCAN        - 扫描WiFi网络");
  Serial.println("  WIFI_SET,SSID,密码 - 设置WiFi账号密码");
  Serial.println("  WIFI_AUTO,1/0    - 设置开机自动连接WiFi");
  Serial.println("  WIFI_CONNECT     - 连接WiFi");
  Serial.println("  WIFI_STATUS      - 查看WiFi状态");
  Serial.println();
}

void loop() {
  // 处理串口输入
  if (Serial.available()) {
    char c = Serial.read();
    if (c == '\n' || c == '\r') {
      if (inputBuffer.length() > 0) {
        processCommand(inputBuffer);
        inputBuffer = "";
      }
    } else {
      inputBuffer += c;
    }
  }
}

// 8位PWM值转换为12位PWM值 (0~255 -> 0~4095)
uint16_t convertPWM8to12(uint8_t pwm8bit) {
  if (pwm8bit == 0) return 0;
  return (uint16_t)((pwm8bit * 4095UL) / 255);
}

// 设置单个电机的PWM值（仅缓存，不立即执行）
void setMotorPWM(int motorNum, uint8_t pwmValue) {
  if (motorNum < 0 || motorNum >= MAX_MOTORS) {
    Serial.println("错误: 电机号超出范围 (0-99)");
    return;
  }

  // 使用直接索引
  int motorIndex = motorNum;

  // 计算对应的芯片和通道
  int chipIndex = motorIndex / CHANNELS_PER_CHIP;
  int channel = motorIndex % CHANNELS_PER_CHIP;

  if (chipIndex >= NUM_PWM_CHIPS) {
    Serial.println("错误: 电机号超出硬件支持范围");
    return;
  }

  // 检查对应的PWM芯片是否可用
  if (!chipAvailable[chipIndex]) {
    Serial.print("错误: 电机 ");
    Serial.print(motorNum);
    Serial.print(" 对应的PWM芯片 #");
    Serial.print(chipIndex + 1);
    Serial.print(" (0x");
    Serial.print(pwmChipAddresses[chipIndex], HEX);
    Serial.println(") 不可用");
    return;
  }

  // 仅保存PWM值到缓存，不立即执行
  motorPWM[motorIndex] = pwmValue;

  Serial.print("电机 ");
  Serial.print(motorNum);
  Serial.print(" PWM缓存设置为 ");
  Serial.print(pwmValue);
  Serial.println(" (需执行START命令生效)");
}

// 应用PWM值到硬件
void applyMotorPWM(int chipIndex, int channel, uint8_t pwmValue) {
  // 检查芯片是否可用
  if (!chipAvailable[chipIndex]) {
    return; // 芯片不可用，跳过
  }

  if (pwmValue == 0) {
    // 关闭电机
    pwmChips[chipIndex].setPWM(channel, 0, 4096);
  } else {
    // 设置PWM值
    uint16_t pwm12bit = convertPWM8to12(pwmValue);
    pwmChips[chipIndex].setPWM(channel, 0, pwm12bit);
  }
}

// 执行所有缓存的PWM值
void startAllMotors() {
  systemRunning = true;

  Serial.println("开始执行所有缓存的PWM值...");

  int appliedCount = 0;
  for (int motorIndex = 0; motorIndex < MAX_MOTORS; motorIndex++) {
    int chipIndex = motorIndex / CHANNELS_PER_CHIP;
    int channel = motorIndex % CHANNELS_PER_CHIP;

    if (chipIndex < NUM_PWM_CHIPS && chipAvailable[chipIndex]) {
      applyMotorPWM(chipIndex, channel, motorPWM[motorIndex]);
      if (motorPWM[motorIndex] > 0) {
        appliedCount++;
      }
    }
  }

  Serial.print("已执行 ");
  Serial.print(appliedCount);
  Serial.println(" 个电机的PWM值");

  Serial.println("所有缓存的PWM值已执行完成");
}

// 停止所有电机输出（设为0）
void stopAllMotors() {
  systemRunning = false;

  Serial.println("停止所有电机输出...");

  // 将所有PWM输出设为0（关闭状态）
  for (int chipIndex = 0; chipIndex < NUM_PWM_CHIPS; chipIndex++) {
    if (chipAvailable[chipIndex]) {
      for (int channel = 0; channel < CHANNELS_PER_CHIP; channel++) {
        pwmChips[chipIndex].setPWM(channel, 0, 4096);
      }
    }
  }

  Serial.println("所有电机输出已停止（注意：缓存的PWM值仍保留）");
}

// 显示系统状态
void showStatus() {
  Serial.println("=== 系统状态 ===");
  Serial.print("系统运行状态: ");
  Serial.println(systemRunning ? "运行中" : "已停止");
  Serial.print("PWM芯片数量: ");
  Serial.println(NUM_PWM_CHIPS);
  Serial.print("可用PWM芯片: ");
  int availableChips = 0;
  for (int i = 0; i < NUM_PWM_CHIPS; i++) {
    if (chipAvailable[i]) {
      Serial.print("0x");
      Serial.print(pwmChipAddresses[i], HEX);
      Serial.print(" ");
      availableChips++;
    }
  }
  Serial.println();
  Serial.print("支持电机数量: ");
  Serial.print(availableChips * CHANNELS_PER_CHIP);
  Serial.print(" (最大");
  Serial.print(MAX_MOTORS);
  Serial.println(")");
  
  // 显示非零PWM值的电机
  Serial.println("当前电机PWM缓存值:");
  bool hasActivePWM = false;
  for (int i = 0; i < MAX_MOTORS; i++) {
    if (motorPWM[i] > 0) {
      Serial.print("  电机 ");
      Serial.print(i + 1);
      Serial.print(": ");
      Serial.println(motorPWM[i]);
      hasActivePWM = true;
    }
  }
  if (!hasActivePWM) {
    Serial.println("  所有电机PWM缓存值为0");
  }
  Serial.println("================");
}













// 处理串口命令
void processCommand(String command) {
  command.trim();
  command.toUpperCase();
  
  if (command == "START") {
    startAllMotors();
  }
  else if (command == "STOP") {
    stopAllMotors();
  }
  else if (command == "STATUS") {
    showStatus();
  }
  else if (command == "WIFI_SCAN") {
    startWiFiScan();
  }
  else if (command == "WIFI_CONNECT") {
    connectToWiFi();
  }
  else if (command == "WIFI_STATUS") {
    showWiFiStatus();
  }


  else if (command.startsWith("SET,")) {
    // 解析SET命令: SET,电机号,PWM值
    int firstComma = command.indexOf(',');
    int secondComma = command.indexOf(',', firstComma + 1);

    if (firstComma > 0 && secondComma > firstComma) {
      String motorStr = command.substring(firstComma + 1, secondComma);
      String pwmStr = command.substring(secondComma + 1);

      int motorNum = motorStr.toInt();
      int pwmValue = pwmStr.toInt();

      if (motorNum >= 0 && motorNum < MAX_MOTORS && pwmValue >= 0 && pwmValue <= 255) {
        setMotorPWM(motorNum, (uint8_t)pwmValue);
      } else {
        Serial.println("错误: 参数超出范围 (电机号:0-99, PWM:0-255)");
      }
    } else {
      Serial.println("错误: SET命令格式错误，应为 SET,电机号,PWM值");
    }
  }

  else if (command.startsWith("WIFI_SET,")) {
    // 解析WIFI_SET命令: WIFI_SET,SSID,密码
    int firstComma = command.indexOf(',');
    int secondComma = command.indexOf(',', firstComma + 1);

    if (firstComma > 0) {
      String ssid, password;
      if (secondComma > firstComma) {
        ssid = command.substring(firstComma + 1, secondComma);
        password = command.substring(secondComma + 1);
      } else {
        ssid = command.substring(firstComma + 1);
        password = "";
      }
      setWiFiCredentials(ssid, password);
    } else {
      Serial.println("Error: WIFI_SET command format error, should be WIFI_SET,SSID,password");
    }
  }
  else if (command.startsWith("WIFI_AUTO,")) {
    // 解析WIFI_AUTO命令: WIFI_AUTO,1/0
    int commaIndex = command.indexOf(',');
    if (commaIndex > 0) {
      String enableStr = command.substring(commaIndex + 1);
      int enableValue = enableStr.toInt();
      if (enableValue == 0 || enableValue == 1) {
        setWiFiAutoConnect(enableValue == 1);
      } else {
        Serial.println("Error: Parameter should be 1 (enable) or 0 (disable)");
      }
    } else {
      Serial.println("Error: WIFI_AUTO command format error, should be WIFI_AUTO,1/0");
    }
  }
  else {
    Serial.println("Error: Unknown command");
    Serial.println("Supported commands:");
    Serial.println("Motor control:");
    Serial.println("  SET,motor,PWM - Set motor PWM cache value");
    Serial.println("  START - Execute all cached PWM values");
    Serial.println("  STOP - Stop all motor output");
    Serial.println("  STATUS - Query system status");
    Serial.println("WiFi functions:");
    Serial.println("  WIFI_SCAN - Scan WiFi networks");
    Serial.println("  WIFI_SET,SSID,password - Set WiFi credentials");
    Serial.println("  WIFI_AUTO,1/0 - Set auto connect on boot");
    Serial.println("  WIFI_CONNECT - Connect to WiFi");
    Serial.println("  WIFI_STATUS - Show WiFi status");
  }
}

// ========== WiFi功能实现 - 基于官方例程 ==========

// 初始化WiFi - 简化版本，避免重启问题
void initializeWiFi() {
  Serial.println("Initializing WiFi...");

  // 初始化Preferences存储
  preferences.begin("motor_ctrl", false);

  // 读取WiFi配置
  wifiSSID = preferences.getString("wifi_ssid", "");
  wifiPassword = preferences.getString("wifi_pass", "");
  wifiAutoConnect = preferences.getBool("wifi_auto", false);

  Serial.print("WiFi SSID: ");
  Serial.println(wifiSSID.length() > 0 ? wifiSSID : "Not set");
  Serial.print("WiFi password: ");
  Serial.println(wifiPassword.length() > 0 ? "Set" : "Not set");
  Serial.print("Auto connect: ");
  Serial.println(wifiAutoConnect ? "Enabled" : "Disabled");

  // 简单的WiFi初始化，不进行预扫描
  WiFi.mode(WIFI_STA);
  delay(100);

  // 如果开启自动连接且有配置，则尝试连接
  if (wifiAutoConnect && wifiSSID.length() > 0) {
    connectToWiFi();
  }

  Serial.println("WiFi initialization done");
}

// WiFi扫描 - 改为同步扫描避免ESP32S3重启问题
void startWiFiScan() {
  if (wifiScanInProgress) {
    Serial.println("WiFi scan already in progress, please wait...");
    return;
  }

  Serial.println("Scan start");
  wifiScanInProgress = true;

  // 确保WiFi处于正确状态
  WiFi.mode(WIFI_STA);
  delay(100);
  WiFi.disconnect(false);
  delay(100);

  // 使用同步扫描，更稳定
  int networksFound = WiFi.scanNetworks();

  // 立即处理结果
  if (networksFound >= 0) {
    printScannedNetworks(networksFound);
  } else {
    Serial.println("WiFi scan failed");
  }

  wifiScanInProgress = false;
}

void printScannedNetworks(uint16_t networksFound) {
  if (networksFound == 0) {
    Serial.println("no networks found");
  } else {
    Serial.println("\nScan done");
    Serial.print(networksFound);
    Serial.println(" networks found");
    Serial.println("Nr | SSID                             | RSSI | CH | Encryption");
    for (int i = 0; i < networksFound; ++i) {
      // Print SSID and RSSI for each network found
      Serial.printf("%2d", i + 1);
      Serial.print(" | ");
      Serial.printf("%-32.32s", WiFi.SSID(i).c_str());
      Serial.print(" | ");
      Serial.printf("%4ld", WiFi.RSSI(i));
      Serial.print(" | ");
      Serial.printf("%2ld", WiFi.channel(i));
      Serial.print(" | ");
      switch (WiFi.encryptionType(i)) {
        case WIFI_AUTH_OPEN:            Serial.print("open"); break;
        case WIFI_AUTH_WEP:             Serial.print("WEP"); break;
        case WIFI_AUTH_WPA_PSK:         Serial.print("WPA"); break;
        case WIFI_AUTH_WPA2_PSK:        Serial.print("WPA2"); break;
        case WIFI_AUTH_WPA_WPA2_PSK:    Serial.print("WPA+WPA2"); break;
        case WIFI_AUTH_WPA2_ENTERPRISE: Serial.print("WPA2-EAP"); break;
        case WIFI_AUTH_WPA3_PSK:        Serial.print("WPA3"); break;
        case WIFI_AUTH_WPA2_WPA3_PSK:   Serial.print("WPA2+WPA3"); break;
        case WIFI_AUTH_WAPI_PSK:        Serial.print("WAPI"); break;
        default:                        Serial.print("unknown");
      }
      Serial.println();
      delay(10);
    }
    Serial.println("");
    // Delete the scan result to free memory for code below.
    WiFi.scanDelete();
  }
}



// WiFi连接功能
void connectToWiFi() {
  if (wifiSSID.length() == 0) {
    Serial.println("Error: WiFi SSID not set");
    return;
  }

  Serial.print("Connecting to WiFi: ");
  Serial.println(wifiSSID);

  // 确保WiFi处于正确状态
  WiFi.mode(WIFI_STA);
  delay(100);
  WiFi.disconnect(false);
  delay(200);

  WiFi.begin(wifiSSID.c_str(), wifiPassword.c_str());

  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println();
    Serial.println("WiFi connected!");
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());
  } else {
    Serial.println();
    Serial.println("WiFi connection failed");
  }
}

// 设置WiFi账号密码
void setWiFiCredentials(String ssid, String password) {
  if (ssid.length() == 0) {
    Serial.println("Error: SSID cannot be empty");
    return;
  }

  wifiSSID = ssid;
  wifiPassword = password;

  // 保存到Flash
  preferences.putString("wifi_ssid", wifiSSID);
  preferences.putString("wifi_pass", wifiPassword);

  Serial.print("WiFi config saved - SSID: ");
  Serial.print(wifiSSID);
  Serial.print(", Password: ");
  Serial.println(wifiPassword.length() > 0 ? "Set" : "None");
}

// 设置WiFi自动连接
void setWiFiAutoConnect(bool enable) {
  wifiAutoConnect = enable;
  preferences.putBool("wifi_auto", wifiAutoConnect);

  Serial.print("Auto connect on boot: ");
  Serial.println(wifiAutoConnect ? "Enabled" : "Disabled");
}

// 显示WiFi状态
void showWiFiStatus() {
  Serial.println("=== WiFi Status ===");
  Serial.print("Connection: ");

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("Connected");
    Serial.print("SSID: ");
    Serial.printf("%.32s\n", WiFi.SSID().c_str());
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());
    Serial.print("Signal strength: ");
    Serial.print(WiFi.RSSI());
    Serial.println(" dBm");
  } else {
    Serial.print("Disconnected (status: ");
    Serial.print(WiFi.status());
    Serial.println(")");
  }

  Serial.print("Configured SSID: ");
  Serial.println(wifiSSID.length() > 0 ? wifiSSID : "Not set");
  Serial.print("Configured password: ");
  Serial.println(wifiPassword.length() > 0 ? "Set" : "Not set");
  Serial.print("Auto connect: ");
  Serial.println(wifiAutoConnect ? "Enabled" : "Disabled");
  Serial.println("==================");
}

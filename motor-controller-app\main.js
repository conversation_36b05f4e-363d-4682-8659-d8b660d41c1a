const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const dgram = require('dgram');

let mainWindow;
let udpClient;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    title: 'Motor Controller',
    resizable: true,
    minimizable: true,
    maximizable: true
  });

  // 加载应用的 index.html
  mainWindow.loadFile('index.html');

  // 开发模式下打开开发者工具
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  // 当窗口关闭时触发
  mainWindow.on('closed', () => {
    mainWindow = null;
    if (udpClient) {
      udpClient.close();
    }
  });
}

// Electron 初始化完成后创建窗口
app.whenReady().then(createWindow);

// 当所有窗口关闭时退出应用 (macOS除外)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在 macOS 上，当点击 dock 图标并且没有其他窗口打开时，
  // 通常在应用程序中重新创建一个窗口
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// UDP通讯处理 - 支持不同的发送格式
function sendUDPCommand(ip, port, command, format = 'newline') {
  return new Promise((resolve, reject) => {
    // 每次都创建新的UDP客户端
    const client = dgram.createSocket('udp4');

    // 设置超时
    const timeout = setTimeout(() => {
      udpClient.close();
      reject(new Error('UDP command timeout'));
    }, 5000);

    udpClient.on('message', (msg, rinfo) => {
      clearTimeout(timeout);
      try {
        const responseText = msg.toString();
        console.log('Raw UDP response:', responseText);
        const response = JSON.parse(responseText);
        console.log('Parsed response:', response);

        // 检查响应状态
        if (response.status === 'error') {
          console.log('ESP32 returned error:', response.message);
          reject(new Error(response.message || 'ESP32 error'));
        } else {
          resolve(response);
        }
      } catch (error) {
        console.log('JSON parse error:', error.message);
        console.log('Raw response:', msg.toString());
        reject(new Error(`Invalid JSON response: ${msg.toString()}`));
      }
      udpClient.close();
    });

    udpClient.on('error', (error) => {
      clearTimeout(timeout);
      reject(error);
      udpClient.close();
    });

    // 根据格式发送命令
    let finalCommand;
    switch (format) {
      case 'plain':
        finalCommand = command.trim();
        break;
      case 'newline':
        finalCommand = command.trim() + '\n';
        break;
      case 'crlf':
        finalCommand = command.trim() + '\r\n';
        break;
      default:
        finalCommand = command.trim() + '\n';
    }

    console.log(`Sending command (${format}): "${finalCommand}" (length: ${finalCommand.length})`);
    const message = Buffer.from(finalCommand, 'utf8');
    udpClient.send(message, 0, message.length, port, ip, (error) => {
      if (error) {
        clearTimeout(timeout);
        reject(error);
        udpClient.close();
      }
    });
  });
}

// IPC 处理器
ipcMain.handle('send-udp-command', async (event, ip, port, command, format = 'newline') => {
  try {
    console.log(`Sending UDP command: ${command} to ${ip}:${port} (format: ${format})`);
    const response = await sendUDPCommand(ip, port, command, format);
    console.log(`UDP response:`, response);
    return { success: true, data: response };
  } catch (error) {
    console.log(`UDP error:`, error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('test-connection', async (event, ip, port) => {
  try {
    const response = await sendUDPCommand(ip, port, 'STATUS', 'newline');
    return { success: true, data: response };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// 调试用的多格式测试
ipcMain.handle('debug-udp-formats', async (event, ip, port, command) => {
  const formats = ['plain', 'newline', 'crlf'];
  const results = [];

  for (const format of formats) {
    try {
      console.log(`Testing format: ${format}`);
      const response = await sendUDPCommand(ip, port, command, format);
      results.push({ format, success: true, data: response });
    } catch (error) {
      results.push({ format, success: false, error: error.message });
    }

    // 延迟避免UDP冲突
    await new Promise(resolve => setTimeout(resolve, 200));
  }

  return results;
});

// 批量发送PWM设置命令
ipcMain.handle('send-pwm-batch', async (event, ip, port, pwmData) => {
  try {
    const results = [];
    
    // 逐个发送SET命令
    for (let i = 0; i < pwmData.length; i++) {
      if (pwmData[i] !== null && pwmData[i] !== undefined) {
        const command = `SET,${i},${pwmData[i]}`;
        const response = await sendUDPCommand(ip, port, command);
        results.push({ motor: i, pwm: pwmData[i], response });
        
        // 添加小延迟避免UDP包丢失
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }
    
    return { success: true, results };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 标题栏 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 24px;
    font-weight: 600;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ff4757;
    transition: background-color 0.3s ease;
}

.status-indicator.connected {
    background-color: #2ed573;
}

/* 连接配置面板 */
.connection-panel {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.connection-config {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.input-group label {
    font-weight: 500;
    color: #555;
}

.input-group input {
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #5a6fd8;
}

.btn-success {
    background-color: #2ed573;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: #26c965;
}

.btn-danger {
    background-color: #ff4757;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: #ff3742;
}

.btn-warning {
    background-color: #ffa502;
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background-color: #ff9500;
}

.btn-secondary {
    background-color: #57606f;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #4a5568;
}

.btn-info {
    background-color: #3742fa;
    color: white;
}

.btn-info:hover:not(:disabled) {
    background-color: #2f3542;
}

.btn-small {
    padding: 5px 10px;
    font-size: 12px;
}

/* 控制面板 */
.control-panel {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.control-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.preset-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.preset-controls input[type="range"] {
    flex: 1;
    max-width: 200px;
}

#presetValueDisplay {
    font-weight: bold;
    min-width: 30px;
    text-align: center;
}

/* PWM矩阵 */
.pwm-matrix {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pwm-matrix h3 {
    margin-bottom: 15px;
    color: #333;
}

.matrix-container {
    overflow-x: auto;
}

.matrix-grid {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 6px;
    min-width: 800px;
}

.pwm-control {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    min-height: 80px;
}

.pwm-control:hover {
    border-color: #667eea;
    background-color: #e3f2fd;
}

.pwm-control.active {
    border-color: #2ed573;
    background-color: #e8f5e8;
}

.pwm-control.error {
    border-color: #ff4757;
    background-color: #ffe3e3;
}

.motor-label {
    font-size: 11px;
    font-weight: bold;
    color: #666;
    margin-bottom: 4px;
}

.pwm-input {
    width: 50px;
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    font-size: 12px;
    font-weight: bold;
    transition: border-color 0.3s ease;
}

.pwm-input:focus {
    outline: none;
    border-color: #667eea;
}

.pwm-input.error {
    border-color: #ff4757;
    background-color: #ffe3e3;
}

/* 状态面板 */
.status-panel {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.status-info, .log-panel {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-info h4, .log-panel h4 {
    margin-bottom: 15px;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 5px;
}

.device-status {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.log-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
    flex-wrap: wrap;
}

.log-controls label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    cursor: pointer;
}

.log-controls input[type="checkbox"] {
    margin: 0;
}

.activity-log {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    height: 250px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    border: 1px solid #e0e0e0;
}

.log-entry {
    margin-bottom: 3px;
    padding: 3px 0;
    border-bottom: 1px solid #eee;
    word-wrap: break-word;
}

.log-entry.sent {
    color: #3742fa;
    background-color: #f0f0ff;
    padding: 3px 6px;
    border-radius: 3px;
}

.log-entry.received {
    color: #2ed573;
    background-color: #f0fff0;
    padding: 3px 6px;
    border-radius: 3px;
}

.log-entry.error {
    color: #ff4757;
    background-color: #fff0f0;
    padding: 3px 6px;
    border-radius: 3px;
}

.log-entry.info {
    color: #667eea;
}

.log-entry.hidden {
    display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .connection-config {
        flex-direction: column;
        align-items: stretch;
    }
    
    .control-buttons {
        flex-direction: column;
    }
    
    .status-panel {
        grid-template-columns: 1fr;
    }
    
    .matrix-grid {
        grid-template-columns: repeat(5, 1fr);
    }
    
    .preset-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
}

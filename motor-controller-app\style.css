/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 标题栏 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 24px;
    font-weight: 600;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ff4757;
    transition: background-color 0.3s ease;
}

.status-indicator.connected {
    background-color: #2ed573;
}

/* 连接配置与控制面板 */
.connection-control-panel {
    background: white;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.left-section {
    flex: 1;
    min-width: 300px;
}

.right-section {
    flex: 2;
    min-width: 400px;
}

.connection-config {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.input-group label {
    font-weight: 500;
    color: #555;
    font-size: 12px;
}

.input-group input {
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
    transition: border-color 0.3s ease;
    width: 120px;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #5a6fd8;
}

.btn-success {
    background-color: #2ed573;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: #26c965;
}

.btn-danger {
    background-color: #ff4757;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: #ff3742;
}

.btn-warning {
    background-color: #ffa502;
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background-color: #ff9500;
}

.btn-secondary {
    background-color: #57606f;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #4a5568;
}

.btn-info {
    background-color: #3742fa;
    color: white;
}

.btn-info:hover:not(:disabled) {
    background-color: #2f3542;
}

.btn-small {
    padding: 5px 10px;
    font-size: 12px;
}

.btn-compact {
    padding: 6px 12px;
    font-size: 12px;
    min-width: 70px;
}

/* 控制按钮 */
.control-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

/* 预设控制面板 */
.preset-panel {
    background: white;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preset-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.preset-controls label {
    font-size: 12px;
    font-weight: 500;
    color: #555;
}

.preset-controls input[type="range"] {
    flex: 1;
    max-width: 150px;
}

#presetValueDisplay {
    font-weight: bold;
    min-width: 25px;
    text-align: center;
    font-size: 12px;
}

/* PWM矩阵 */
.pwm-matrix {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pwm-matrix h3 {
    margin-bottom: 10px;
    color: #333;
    font-size: 16px;
}

.matrix-container {
    overflow-x: auto;
}

.matrix-grid {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 3px;
    min-width: 600px;
    max-width: 800px;
}

.pwm-control {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 4px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    min-height: 50px;
}

.pwm-control:hover {
    border-color: #667eea;
    background-color: #e3f2fd;
}

.pwm-control.active {
    border-color: #2ed573;
    background-color: #e8f5e8;
}

.pwm-control.error {
    border-color: #ff4757;
    background-color: #ffe3e3;
}

.motor-label {
    font-size: 9px;
    font-weight: bold;
    color: #666;
    margin-bottom: 2px;
    line-height: 1;
}

.pwm-input {
    width: 40px;
    padding: 2px;
    border: 1px solid #ddd;
    border-radius: 3px;
    text-align: center;
    font-size: 10px;
    font-weight: bold;
    transition: border-color 0.3s ease;
}

.pwm-input:focus {
    outline: none;
    border-color: #667eea;
}

.pwm-input.error {
    border-color: #ff4757;
    background-color: #ffe3e3;
}

/* 状态面板 */
.status-panel {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.status-info, .log-panel {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-info h4, .log-panel h4 {
    margin-bottom: 15px;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 5px;
}

.device-status {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.log-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
    flex-wrap: wrap;
}

.log-controls label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    cursor: pointer;
}

.log-controls input[type="checkbox"] {
    margin: 0;
}

.activity-log {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    height: 200px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 10px;
    border: 1px solid #e0e0e0;
}

.log-entry {
    margin-bottom: 3px;
    padding: 3px 0;
    border-bottom: 1px solid #eee;
    word-wrap: break-word;
}

.log-entry.sent {
    color: #3742fa;
    background-color: #f0f0ff;
    padding: 3px 6px;
    border-radius: 3px;
}

.log-entry.received {
    color: #2ed573;
    background-color: #f0fff0;
    padding: 3px 6px;
    border-radius: 3px;
}

.log-entry.error {
    color: #ff4757;
    background-color: #fff0f0;
    padding: 3px 6px;
    border-radius: 3px;
}

.log-entry.info {
    color: #667eea;
}

.log-entry.hidden {
    display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .connection-control-panel {
        flex-direction: column;
        gap: 10px;
    }

    .left-section, .right-section {
        min-width: auto;
        flex: none;
    }

    .connection-config {
        justify-content: center;
    }

    .control-buttons {
        justify-content: center;
    }

    .input-group input {
        width: 100px;
    }
    
    .status-panel {
        grid-template-columns: 1fr;
    }
    
    .matrix-grid {
        grid-template-columns: repeat(5, 1fr);
        min-width: 300px;
    }

    .pwm-input {
        width: 35px;
        font-size: 9px;
    }

    .motor-label {
        font-size: 8px;
    }
    
    .preset-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
}
